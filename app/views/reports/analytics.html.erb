<%= component "page" do |page| %>
  <% page.with_header title: "Analytics Dashboard" do %>
    <div class="flex items-center space-x-4">
      <%= link_to reports_path,
          class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
        <%= icon name: "chevron-left", class: "h-4 w-4 mr-2" %>
        Back to Reports
      <% end %>

      <button
        onclick="refreshAllCharts()"
        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
      >
        <%= icon name: "refresh-cw", class: "h-4 w-4 mr-2" %>
        Refresh Data
      </button>

      <div class="relative" x-data="{ open: false }">
        <button
          @click="open = !open"
          type="button"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          <%= icon name: "download", class: "h-4 w-4 mr-2" %>
          Export Dashboard
          <%= icon name: "chevron-down", class: "h-4 w-4 ml-2" %>
        </button>

        <div
          x-show="open"
          @click.away="open = false"
          x-transition:enter="transition ease-out duration-100"
          x-transition:enter-start="transform opacity-0 scale-95"
          x-transition:enter-end="transform opacity-100 scale-100"
          x-transition:leave="transition ease-in duration-75"
          x-transition:leave-start="transform opacity-100 scale-100"
          x-transition:leave-end="transform opacity-0 scale-95"
          class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
        >
          <div class="py-1">
            <button onclick="exportDashboard('pdf')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              Export as PDF
            </button>
            <button onclick="exportDashboard('png')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              Export as Image
            </button>
            <button onclick="exportDashboard('data')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              Export Raw Data
            </button>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Metrics Dashboard Component -->
  <%= render Reports::MetricsDashboardComponent.new(
    user: current_user,
    date_range: @date_range,
    refresh_interval: 60000
  ) %>

  <!-- Advanced Analytics Section -->
  <div class="mt-8 space-y-6">
    <!-- Sales Performance Deep Dive -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Sales Performance Analysis</h3>
        <p class="text-sm text-gray-600 mt-1">Detailed breakdown of sales metrics and trends</p>
      </div>

      <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Sales by Product Category -->
          <div id="sales-by-category-chart">
            <%= render Charts::BarChartComponent.new(
              title: "Sales by Product Category",
              subtitle: "Top performing categories",
              data: [],
              height: "300px",
              refresh_url: sales_analytics_reports_path(format: :json),
              auto_refresh: true,
              chart_id: "sales-category-chart"
            ) %>
          </div>

          <!-- Sales Trend Over Time -->
          <div id="sales-trend-chart">
            <%= render Charts::LineChartComponent.new(
              title: "Sales Trend",
              subtitle: "Daily sales performance",
              data: [],
              height: "300px",
              smooth: true,
              fill: true,
              colors: ["#3b82f6"],
              refresh_url: sales_analytics_reports_path(format: :json),
              auto_refresh: true,
              chart_id: "sales-trend-chart"
            ) %>
          </div>
        </div>
      </div>
    </div>

    <!-- User Engagement Analytics -->
    <% if current_user.admin? || current_user.super_admin? %>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">User Engagement Analytics</h3>
          <p class="text-sm text-gray-600 mt-1">User activity and engagement metrics</p>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- User Activity Heatmap -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-4">Activity Heatmap</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-center">
                  <%= icon name: "calendar", class: "h-8 w-8 text-gray-400 mx-auto mb-2" %>
                  <p class="text-sm text-gray-500">Activity heatmap visualization</p>
                  <p class="text-xs text-gray-400">Coming soon</p>
                </div>
              </div>
            </div>

            <!-- User Growth Chart -->
            <div id="user-growth-chart">
              <%= render Charts::LineChartComponent.new(
                title: "User Growth",
                subtitle: "New user registrations over time",
                data: [],
                height: "300px",
                smooth: true,
                colors: ["#10b981"],
                refresh_url: user_analytics_reports_path(format: :json),
                auto_refresh: true,
                chart_id: "user-growth-chart"
              ) %>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Performance Insights -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Performance Insights</h3>
        <p class="text-sm text-gray-600 mt-1">AI-powered insights and recommendations</p>
      </div>

      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Insight Cards -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <%= icon name: "chart-line-up", class: "h-6 w-6 text-blue-600" %>
              </div>
              <div class="ml-3">
                <h4 class="text-sm font-medium text-blue-900">Sales Trending Up</h4>
                <p class="text-sm text-blue-700 mt-1">Sales have increased by 15% compared to last month. Keep up the great work!</p>
              </div>
            </div>
          </div>

          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <%= icon name: "alert-triangle", class: "h-6 w-6 text-yellow-600" %>
              </div>
              <div class="ml-3">
                <h4 class="text-sm font-medium text-yellow-900">Pending Orders</h4>
                <p class="text-sm text-yellow-700 mt-1">You have 12 orders pending approval. Consider reviewing them soon.</p>
              </div>
            </div>
          </div>

          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <%= icon name: "target", class: "h-6 w-6 text-green-600" %>
              </div>
              <div class="ml-3">
                <h4 class="text-sm font-medium text-green-900">Goal Achievement</h4>
                <p class="text-sm text-green-700 mt-1">You're 85% towards your monthly goal. Just 3 more sales to go!</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<% end %>

<script>
  function refreshAllCharts() {
    // Refresh all charts on the page
    Object.keys(window.chartInstances || {}).forEach(chartId => {
      const container = document.getElementById(chartId);
      if (container && container.dataset.refreshUrl) {
        refreshChart(chartId, container.dataset.refreshUrl);
      }
    });

    // Show global refresh indicator
    const indicator = document.createElement('div');
    indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
    indicator.textContent = 'All charts refreshed';
    document.body.appendChild(indicator);

    setTimeout(() => {
      if (indicator.parentElement) {
        indicator.parentElement.removeChild(indicator);
      }
    }, 3000);
  }

  function exportDashboard(format) {
    // Implementation for dashboard export
    console.log(`Exporting dashboard as ${format}`);
    // This would integrate with a service to generate dashboard exports
  }

  // Load initial chart data
  document.addEventListener('DOMContentLoaded', function() {
    // Load sales analytics data
    fetch('<%= sales_analytics_reports_path(format: :json) %>')
      .then(response => response.json())
      .then(data => {
        // Update charts with real data
        updateChartData('sales-category-chart', data.top_products);
        updateChartData('sales-trend-chart', data.daily_sales);
      })
      .catch(error => console.error('Error loading sales data:', error));

    <% if current_user.admin? || current_user.super_admin? %>
      // Load user analytics data
      fetch('<%= user_analytics_reports_path(format: :json) %>')
        .then(response => response.json())
        .then(data => {
          updateChartData('user-growth-chart', data.new_registrations);
        })
        .catch(error => console.error('Error loading user data:', error));
    <% end %>
  });

  function updateChartData(chartId, data) {
    const chart = window.chartInstances[chartId];
    if (chart && data) {
      // Transform data based on chart type and update
      chart.data = transformDataForChart(data, chart.config.type);
      chart.update();
    }
  }

  function transformDataForChart(data, chartType) {
    // Transform raw data into chart.js format
    if (Array.isArray(data)) {
      return {
        labels: data.map((item, index) => item.label || `Item ${index + 1}`),
        datasets: [{
          data: data.map(item => item.value || item),
          backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
        }]
      };
    }
    return data;
  }
</script>
